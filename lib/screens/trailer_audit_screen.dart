import 'package:flutter/cupertino.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:spot_on/providers/trailer_audit_state.dart';
import 'package:spot_on/utils/imports.dart';
import '../models/spot_on_locations.dart';
import '../models/truck_list_model.dart';
import '../providers/create_job_state.dart';

class TrailerAuditScreen extends StatefulWidget {
  const TrailerAuditScreen({Key? key}) : super(key: key);

  final String screenTitle = "Trailer Audit";

  @override
  State<TrailerAuditScreen> createState() => _TrailerAuditScreenState();
}

class _TrailerAuditScreenState extends State<TrailerAuditScreen> {
  var controller = TextEditingController();
  bool isTablet = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      TrailerAuditState trailerAuditState = context.read<TrailerAuditState>();
      trailerAuditState.clearAll();
      trailerAuditState.getTrailerAudit();
      trailerAuditState.getSpotOnLocations();
      trailerAuditState.refresh();
    });
  }

  @override
  Widget build(BuildContext context) {
    TrailerAuditState trailerAuditState = context.watch<TrailerAuditState>();
    var items = trailerAuditState.trailerAudit?.list ?? [];

    // Check if device is tablet based on width
    isTablet = MediaQuery.of(context).size.width > 600;

    return Scaffold(
      // appBar: AppBar(
      //   title: const Text(
      //     'Trailer Audit',
      //     style: TextStyle(
      //       color: Colors.black,
      //       fontWeight: FontWeight.bold,
      //     ),
      //   ),
      //   backgroundColor: Colors.white,
      //   elevation: 0,
      // ),
      body: ModalProgressHUD(
        inAsyncCall: trailerAuditState.isLoading,
        opacity: 0,
        color: Colors.white,
        child: Container(
          color: Colors.grey[50],
          child: Column(
            children: [
              // Location dropdown
              Container(
                color: Colors.white,
                padding: const EdgeInsets.all(16),
                child: DropdownButtonFormField<SpotOnLocation>(
                  decoration: const InputDecoration(
                    labelText: 'Location',
                    border: OutlineInputBorder(),
                  ),
                  value: trailerAuditState.selectedPickUpLocation,
                  icon: trailerAuditState.isLoading
                      ? const CupertinoActivityIndicator()
                      : const Icon(Icons.arrow_drop_down),
                  isExpanded: true,
                  onChanged: (SpotOnLocation? spotOnLocation) {
                    if (trailerAuditState.isLoading) return;
                    trailerAuditState.selectedPickupSpot = null;
                    trailerAuditState.selectedPickUpLocation = spotOnLocation;
                    trailerAuditState.area = spotOnLocation?.locationName;
                    if (spotOnLocation != null) {
                      trailerAuditState.getSpots(
                        locationId: spotOnLocation.locationId!,
                        drop: false
                      );
                    }
                    trailerAuditState.refresh();
                  },
                  items: trailerAuditState.spotOnLocationList.list
                      .map<DropdownMenuItem<SpotOnLocation>>((SpotOnLocation value) {
                    return DropdownMenuItem<SpotOnLocation>(
                      value: value,
                      child: Text(value.locationName ?? ''),
                    );
                  }).toList(),
                ),
              ),

              // Dock entries list (when location is selected)
              Expanded(
                child: trailerAuditState.selectedPickUpLocation != null && trailerAuditState.dockEntries.isNotEmpty
                    ? ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: trailerAuditState.dockEntries.length,
                        itemBuilder: (context, index) {
                          var dockEntry = trailerAuditState.dockEntries[index];
                          return Card(
                            margin: const EdgeInsets.only(bottom: 16),
                            elevation: 1,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Dock and Status in same row with underline style
                                  Row(
                                    children: [
                                      // Dock section
                                      Expanded(
                                        flex: 1,
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            const Text(
                                              'Dock',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.grey,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Container(
                                              width: double.infinity,
                                              padding: const EdgeInsets.symmetric(vertical: 8),
                                              decoration: const BoxDecoration(
                                                border: Border(
                                                  bottom: BorderSide(color: Colors.grey, width: 1),
                                                ),
                                              ),
                                              child: Text(
                                                dockEntry.spot.spotName ?? '',
                                                style: const TextStyle(
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 16),

                                      // Status section
                                      Expanded(
                                        flex: 1,
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            const Text(
                                              'Status',
                                              style: TextStyle(
                                                fontSize: 12,
                                                color: Colors.grey,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Container(
                                              decoration: const BoxDecoration(
                                                border: Border(
                                                  bottom: BorderSide(color: Colors.grey, width: 1),
                                                ),
                                              ),
                                              child: DropdownButtonFormField<FleetStatus>(
                                                decoration: const InputDecoration(
                                                  border: InputBorder.none,
                                                  contentPadding: EdgeInsets.symmetric(vertical: 8),
                                                ),
                                                value: dockEntry.selectedFleetStatus,
                                                isExpanded: true,
                                                hint: const Text('Select Status'),
                                                onChanged: (FleetStatus? status) {
                                                  trailerAuditState.updateDockEntry(
                                                    index,
                                                    fleetStatus: status,
                                                  );
                                                },
                                                items: trailerAuditState.fleetStatuses
                                                    .map<DropdownMenuItem<FleetStatus>>((FleetStatus status) {
                                                  return DropdownMenuItem<FleetStatus>(
                                                    value: status,
                                                    child: Text(status.name),
                                                  );
                                                }).toList(),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 20),

                                  // Trailer section with underline style
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'Trailer',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Container(
                                        decoration: const BoxDecoration(
                                          border: Border(
                                            bottom: BorderSide(color: Colors.grey, width: 1),
                                          ),
                                        ),
                                        child: DropdownButtonFormField<TruckDetail>(
                                          decoration: const InputDecoration(
                                            border: InputBorder.none,
                                            contentPadding: EdgeInsets.symmetric(vertical: 8),
                                          ),
                                          value: dockEntry.selectedTruckDetail,
                                          isExpanded: true,
                                          hint: const Text('Select Trailer'),
                                          onChanged: (TruckDetail? truck) {
                                            trailerAuditState.updateDockEntry(
                                              index,
                                              truckDetail: truck,
                                            );
                                          },
                                          items: trailerAuditState.truckListModel.list
                                              .map<DropdownMenuItem<TruckDetail>>((TruckDetail truck) {
                                            return DropdownMenuItem<TruckDetail>(
                                              value: truck,
                                              child: Text(truck.unitNumber ?? ''),
                                            );
                                          }).toList(),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 20),

                                  // Notes section with underline style
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'Notes',
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.grey,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Container(
                                        decoration: const BoxDecoration(
                                          border: Border(
                                            bottom: BorderSide(color: Colors.grey, width: 1),
                                          ),
                                        ),
                                        child: TextFormField(
                                          decoration: const InputDecoration(
                                            border: InputBorder.none,
                                            contentPadding: EdgeInsets.symmetric(vertical: 8),
                                            hintText: 'Enter notes...',
                                          ),
                                          initialValue: dockEntry.notes,
                                          maxLines: 2,
                                          onChanged: (String value) {
                                            trailerAuditState.updateDockEntry(
                                              index,
                                              notes: value,
                                            );
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),

                                  // Clear button
                                  Align(
                                    alignment: Alignment.centerRight,
                                    child: TextButton.icon(
                                      onPressed: () {
                                        trailerAuditState.clearDockEntry(index);
                                      },
                                      icon: const Icon(Icons.close, color: Colors.red, size: 16),
                                      label: const Text('Clear', style: TextStyle(color: Colors.red)),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      )
                    : trailerAuditState.selectedPickUpLocation != null
                        ? const Center(
                            child: Text("No docks found for selected location!"),
                          )
                        : items.isNotEmpty
                            ? ListView.builder(
                                padding: const EdgeInsets.all(16),
                                itemCount: items.length,
                                itemBuilder: (context, index) {
                                  var item = items[index];
                                  return Card(
                                    margin: const EdgeInsets.only(bottom: 16),
                                    elevation: 1,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(16),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text('Location: ${item.area ?? ''}',
                                               style: const TextStyle(fontWeight: FontWeight.bold)),
                                          const SizedBox(height: 8),
                                          Text('Dock: ${item.slot ?? ''}'),
                                          Text('Trailer: ${item.trailerNumber ?? ''}'),
                                          Text('Status: ${item.trailerStatus ?? ''}'),
                                          if (item.notes != null && item.notes!.isNotEmpty)
                                            Text('Notes: ${item.notes}'),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              )
                            : const Center(
                                child: Text("Select a location to manage dock entries or view all trailer audits!"),
                              ),
              ),

              // Save button
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                child: ElevatedButton(
                  onPressed: () {
                    if (trailerAuditState.selectedPickUpLocation != null &&
                        trailerAuditState.dockEntries.isNotEmpty) {
                      // Save dock entries when location is selected
                      trailerAuditState.saveDockEntries();
                    } else {
                      showToast("Please select a location first!");
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.greenAccent,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'SAVE',
                    style: TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      backgroundColor: Colors.white,
    );
  }
}
