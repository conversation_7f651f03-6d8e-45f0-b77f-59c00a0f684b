import 'package:flutter/cupertino.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:spot_on/providers/trailer_audit_state.dart';
import 'package:spot_on/utils/gaps.dart';
import 'package:spot_on/utils/imports.dart';
import '../models/spot_on_locations.dart';
import '../models/spots_list.dart';
import '../models/truck_list_model.dart';
import '../providers/create_job_state.dart';
import '../widgets/add_trailer.dart';

class TrailerAuditScreen extends StatefulWidget {
  const TrailerAuditScreen({Key? key}) : super(key: key);

  final String screenTitle = "Trailer Audit";

  @override
  State<TrailerAuditScreen> createState() => _TrailerAuditScreenState();
}

class _TrailerAuditScreenState extends State<TrailerAuditScreen> {
  var controller = TextEditingController();
  bool isTablet = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      TrailerAuditState trailerAuditState = context.read<TrailerAuditState>();
      trailerAuditState.clearAll();
      trailerAuditState.getTrailerAudit();
      trailerAuditState.getSpotOnLocations();
      trailerAuditState.refresh();
    });
  }

  @override
  Widget build(BuildContext context) {
    TrailerAuditState trailerAuditState = context.watch<TrailerAuditState>();
    var items = trailerAuditState.trailerAudit?.list ?? [];
    
    // Check if device is tablet based on width
    isTablet = MediaQuery.of(context).size.width > 600;
    
    return Scaffold(
      // appBar: AppBar(
      //   title: const Text(
      //     'Trailer Audit',
      //     style: TextStyle(
      //       color: Colors.black,
      //       fontWeight: FontWeight.bold,
      //     ),
      //   ),
      //   backgroundColor: Colors.white,
      //   elevation: 0,
      // ),
      body: ModalProgressHUD(
        inAsyncCall: trailerAuditState.isLoading,
        opacity: 0,
        color: Colors.white,
        child: Container(
          color: Colors.grey[50],
          child: Column(
            children: [
              // Location dropdown
              Container(
                color: Colors.white,
                padding: const EdgeInsets.all(16),
                child: DropdownButtonFormField<SpotOnLocation>(
                  decoration: const InputDecoration(
                    labelText: 'Location',
                    border: OutlineInputBorder(),
                  ),
                  value: trailerAuditState.selectedPickUpLocation,
                  icon: trailerAuditState.isLoading 
                      ? const CupertinoActivityIndicator() 
                      : const Icon(Icons.arrow_drop_down),
                  isExpanded: true,
                  onChanged: (SpotOnLocation? spotOnLocation) {
                    if (trailerAuditState.isLoading) return;
                    trailerAuditState.selectedPickupSpot = null;
                    trailerAuditState.selectedPickUpLocation = spotOnLocation;
                    trailerAuditState.area = spotOnLocation?.locationName;
                    if (spotOnLocation != null) {
                      trailerAuditState.getSpots(
                        locationId: spotOnLocation.locationId!, 
                        drop: false
                      );
                    }
                    trailerAuditState.refresh();
                  },
                  items: trailerAuditState.spotOnLocationList.list
                      .map<DropdownMenuItem<SpotOnLocation>>((SpotOnLocation value) {
                    return DropdownMenuItem<SpotOnLocation>(
                      value: value,
                      child: Text(value.locationName ?? ''),
                    );
                  }).toList(),
                ),
              ),
              
              // Trailer audit list
              Expanded(
                child: items.isNotEmpty
                    ? ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: items.length,
                        itemBuilder: (context, index) {
                          var item = items[index];
                          return Card(
                            margin: const EdgeInsets.only(bottom: 16),
                            elevation: 1,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Dock, Trailer, Status row
                                  isTablet
                                      ? Row(
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  const Text('Dock', style: TextStyle(fontSize: 12, color: Colors.grey)),
                                                  Text(item.slot ?? '', style: const TextStyle(fontWeight: FontWeight.bold)),
                                                ],
                                              ),
                                            ),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  const Text('Trailer', style: TextStyle(fontSize: 12, color: Colors.grey)),
                                                  Text(item.trailerNumber ?? '', style: const TextStyle(fontWeight: FontWeight.bold)),
                                                ],
                                              ),
                                            ),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  const Text('Status', style: TextStyle(fontSize: 12, color: Colors.grey)),
                                                  Text(item.trailerStatus ?? '', style: const TextStyle(fontWeight: FontWeight.bold)),
                                                ],
                                              ),
                                            ),
                                          ],
                                        )
                                      : Column(
                                          children: [
                                            Row(
                                              children: [
                                                Expanded(
                                                  flex: 1,
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      const Text('Dock', style: TextStyle(fontSize: 12, color: Colors.grey)),
                                                      Text(item.slot ?? '', style: const TextStyle(fontWeight: FontWeight.bold)),
                                                    ],
                                                  ),
                                                ),
                                                Expanded(
                                                  flex: 1,
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      const Text('Trailer', style: TextStyle(fontSize: 12, color: Colors.grey)),
                                                      Text(item.trailerNumber ?? '', style: const TextStyle(fontWeight: FontWeight.bold)),
                                                    ],
                                                  ),
                                                ),
                                                Expanded(
                                                  flex: 1,
                                                  child: Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      const Text('Status', style: TextStyle(fontSize: 12, color: Colors.grey)),
                                                      Text(item.trailerStatus ?? '', style: const TextStyle(fontWeight: FontWeight.bold)),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                  
                                  const Divider(height: 24),
                                  
                                  // Notes section
                                  const Text('Notes', style: TextStyle(fontSize: 12, color: Colors.grey)),
                                  Text(item.notes ?? 'No notes', style: const TextStyle(fontSize: 14)),
                                  
                                  // Clear button
                                  Align(
                                    alignment: Alignment.centerRight,
                                    child: TextButton.icon(
                                      onPressed: () {
                                        // Add clear functionality here
                                      },
                                      icon: const Icon(Icons.close, color: Colors.red, size: 16),
                                      label: const Text('Clear', style: TextStyle(color: Colors.red)),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      )
                    : const Center(
                        child: Text("No trailer audit found!"),
                      ),
              ),
              
              // Save button
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                child: ElevatedButton(
                  onPressed: () {
                    if (trailerAuditState.selectedTruckDetail == null ||
                        trailerAuditState.area == null ||
                        trailerAuditState.slot == null ||
                        trailerAuditState.spotid == null ||
                        trailerAuditState.trailerStatus == null) {
                      showToast("Fill all required fields!");
                      return;
                    }
                    trailerAuditState.postTrailerAudit();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.greenAccent,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text(
                    'SAVE',
                    style: TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      backgroundColor: Colors.white,
    );
  }
}
