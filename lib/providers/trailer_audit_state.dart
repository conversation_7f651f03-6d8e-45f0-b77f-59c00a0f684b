import 'package:autocomplete_textfield/autocomplete_textfield.dart';
import 'package:spot_on/models/traileraudit/get_trailer_audit.dart';
import 'package:spot_on/models/traileraudit/post_trailer_audit.dart';
import 'package:spot_on/models/traileraudit/bulk_trailer_audit.dart';
import 'package:spot_on/models/truck_list_model.dart';
import 'package:spot_on/utils/imports.dart';

import '../models/cliient_list_model.dart';
import '../models/spot_on_locations.dart';
import '../models/spots_list.dart';
import '../utils/app_logger.dart';
import 'create_job_state.dart';

// Class to represent individual dock entries
class DockEntry {
  final Spot spot;
  TruckDetail? selectedTruckDetail;
  FleetStatus? selectedFleetStatus;
  String? notes;
  ListElement? existingAudit; // Reference to existing trailer audit data
  late TextEditingController notesController;

  // Original values from spot data when location is selected (baseline)
  String? originalFleetId;        // From spot.fleet.fleetId
  String? originalFleetStatus;    // From spot.fleet.fleetStatus
  String? originalNotes;          // From spot.fleet.remarks or spot.remarks
  String? originalUnitNumber;     // From spot.fleet.unitNumber

  DockEntry({
    required this.spot,
    this.selectedTruckDetail,
    this.selectedFleetStatus,
    this.notes,
    this.existingAudit,
  }) {
    notesController = TextEditingController(text: notes ?? '');
    // Store original values from spot data as baseline
    _storeOriginalSpotData();
  }

  // Store original values from spot data
  void _storeOriginalSpotData() {
    if (spot.fleet != null) {
      originalFleetId = spot.fleet!.fleetId;
      originalFleetStatus = spot.fleet!.fleetStatus;
      originalNotes = spot.fleet!.remarks;
      originalUnitNumber = spot.fleet!.unitNumber;
    } else {
      originalFleetId = null;
      originalFleetStatus = null;
      originalNotes = spot.remarks;
      originalUnitNumber = null;
    }

    printLog('Stored original data for ${spot.spotName}: FleetId=${originalFleetId}, Status=${originalFleetStatus}, UnitNumber=${originalUnitNumber}, Notes="${originalNotes}"');
  }

  void clear() {
    selectedTruckDetail = null;
    selectedFleetStatus = null;
    notes = null;
    notesController.clear();
  }

  void updateNotes(String newNotes) {
    notes = newNotes;
    if (notesController.text != newNotes) {
      notesController.text = newNotes;
    }
  }

  // Check if current form values are different from original spot data
  bool hasChanges() {
    // Compare current form values with original spot data
    String currentFleetId = selectedTruckDetail?.fleetId ?? '';
    String currentFleetStatus = selectedFleetStatus?.id ?? '';
    String currentNotes = notes ?? '';
    String currentUnitNumber = selectedTruckDetail?.unitNumber ?? '';

    bool fleetIdChanged = currentFleetId != (originalFleetId ?? '');
    bool statusChanged = currentFleetStatus != (originalFleetStatus ?? '');
    bool notesChanged = currentNotes != (originalNotes ?? '');
    bool unitNumberChanged = currentUnitNumber != (originalUnitNumber ?? '');

    bool hasChanges = fleetIdChanged || statusChanged || notesChanged || unitNumberChanged;

    if (hasChanges) {
      printLog('Changes detected for ${spot.spotName}:');
      printLog('  FleetId: "${originalFleetId ?? ''}" → "${currentFleetId}"');
      printLog('  Status: "${originalFleetStatus ?? ''}" → "${currentFleetStatus}"');
      printLog('  UnitNumber: "${originalUnitNumber ?? ''}" → "${currentUnitNumber}"');
      printLog('  Notes: "${originalNotes ?? ''}" → "${currentNotes}"');
    }

    return hasChanges;
  }

  // Check if all required fields are filled for this entry
  bool hasAllRequiredFields() {
    return selectedTruckDetail != null && selectedFleetStatus != null;
  }

  // Update original values (call this after successful save)
  void updateOriginalValues() {
    originalFleetId = selectedTruckDetail?.fleetId;
    originalFleetStatus = selectedFleetStatus?.id;
    originalNotes = notes;
    originalUnitNumber = selectedTruckDetail?.unitNumber;
  }

  void dispose() {
    notesController.dispose();
  }
}

class TrailerAuditState extends ChangeNotifier {
  TruckDetail? selectedTruckDetail;
  String? area;
  String? slot;
  String? spotid;
  String? trailerStatus;
  String? notes;
  String? carrier;
  bool isLoading = false;
  GetTrailerAudit? trailerAudit;
  FleetStatus? selectedFleetStatus;
  List<FleetStatus> fleetStatuses = [const FleetStatus('EMPTY', 'Empty'), const FleetStatus('LOADED', 'Loaded'), const FleetStatus('PROCESSING', 'Processing')];

  Spot? selectedPickupSpot;
  SpotOnLocation? selectedPickUpLocation;
  SpotOnLocationList spotOnLocationList = SpotOnLocationList(list: []);
  SpotsList pickupSpotsList = SpotsList(list: []);

  // New properties for dock management
  List<DockEntry> dockEntries = [];
  TruckListModel truckListModel = TruckListModel(list: []);

  GlobalKey<AutoCompleteTextFieldState<TruckDetail>> autoCompleteKey = GlobalKey();
  TextEditingController autoCompleteController = TextEditingController();

  refresh() async {
    notifyListeners();
  }

  showLoading() {
    isLoading = true;
    notifyListeners();
  }

  hideLoading() {
    isLoading = false;
    notifyListeners();
  }

  clearAll() async {
    selectedTruckDetail = null;
    area = null;
    slot = null;
    trailerStatus = null;
    notes = null;
    carrier = null;
    trailerAudit = null;
    autoCompleteController.clear();
    selectedPickupSpot = null;
    selectedPickUpLocation = null;
    spotOnLocationList = SpotOnLocationList(list: []);
    pickupSpotsList = SpotsList(list: []);
    selectedFleetStatus = null;

    // Dispose controllers before clearing
    for (var entry in dockEntries) {
      entry.dispose();
    }
    dockEntries = [];
    truckListModel = TruckListModel(list: []);
    notifyListeners();
  }

  // Method to create dock entries when location is selected
  void createDockEntries() {
    if (selectedPickUpLocation == null || pickupSpotsList.list.isEmpty) {
      dockEntries = [];
      notifyListeners();
      return;
    }

    dockEntries = pickupSpotsList.list.map((spot) {
      DockEntry entry = DockEntry(
        spot: spot,
      );

      // Prefill with data from the location response (spot.fleet)
      if (spot.fleet != null) {
        printLog('Prefilling dock ${spot.spotName} with fleet data from location response');
        printLog('Fleet Status: ${spot.fleet!.fleetStatus}, Unit Number: ${spot.fleet!.unitNumber}, Remarks: ${spot.fleet!.remarks}');

        // 1. Prefill Status from fleet.fleetStatus
        if (spot.fleet!.fleetStatus != null) {
          try {
            entry.selectedFleetStatus = fleetStatuses.firstWhere(
              (status) => status.id.toUpperCase() == spot.fleet!.fleetStatus!.toUpperCase(),
            );
            printLog('Found matching status: ${entry.selectedFleetStatus?.name}');
          } catch (e) {
            printLog('No matching status found for: ${spot.fleet!.fleetStatus}');
            entry.selectedFleetStatus = null;
          }
        }

        // 2. Prefill Trailer from fleet.unitNumber
        if (spot.fleet!.unitNumber != null && truckListModel.list.isNotEmpty) {
          try {
            entry.selectedTruckDetail = truckListModel.list.firstWhere(
              (truck) => truck.unitNumber == spot.fleet!.unitNumber,
            );
            printLog('Found matching truck: ${entry.selectedTruckDetail?.unitNumber}');
          } catch (e) {
            printLog('No matching truck found for unit number: ${spot.fleet!.unitNumber}');
            entry.selectedTruckDetail = null;
          }
        }

        // 3. Prefill Notes from fleet.remarks
        entry.notes = spot.fleet!.remarks;
        entry.notesController.text = spot.fleet!.remarks ?? '';
      } else if (spot.remarks != null) {
        // Fallback: use spot.remarks if fleet.remarks is not available
        entry.notes = spot.remarks;
        entry.notesController.text = spot.remarks ?? '';
        printLog('Using spot remarks as notes for dock ${spot.spotName}: ${spot.remarks}');
      } else {
        printLog('No fleet data found for dock: ${spot.spotName}');
      }

      // Also check for existing trailer audit data as secondary source
      if (trailerAudit?.list != null) {
        try {
          ListElement? existingAudit = trailerAudit!.list!.firstWhere(
            (audit) => audit.area == selectedPickUpLocation!.locationName &&
                      audit.slot == spot.spotName,
          );

          // Only use audit data if fleet data is not available
          if (spot.fleet == null) {
            printLog('Using trailer audit data as fallback for dock ${spot.spotName}');

            if (existingAudit.trailerNumber != null && truckListModel.list.isNotEmpty && entry.selectedTruckDetail == null) {
              try {
                entry.selectedTruckDetail = truckListModel.list.firstWhere(
                  (truck) => truck.unitNumber == existingAudit.trailerNumber,
                );
              } catch (e) {
                // Ignore if not found
              }
            }

            if (existingAudit.trailerStatus != null && entry.selectedFleetStatus == null) {
              try {
                entry.selectedFleetStatus = fleetStatuses.firstWhere(
                  (status) => status.id == existingAudit.trailerStatus,
                );
              } catch (e) {
                // Ignore if not found
              }
            }

            entry.notes ??= existingAudit.notes;
          }
        } catch (e) {
          // No existing audit data found
        }
      }

      return entry;
    }).toList();

    printLog('Created ${dockEntries.length} dock entries for location: ${selectedPickUpLocation!.locationName}');
    notifyListeners();
  }

  // Method to clear a specific dock entry via API
  clearDockEntry(int index) async {
    if (index >= 0 && index < dockEntries.length) {
      DockEntry entry = dockEntries[index];

      showLoading();

      // Call clear API with spotId
      var result = await Services.clearTrailerAudit(spotId: entry.spot.spotId!);

      if (result is Success) {
        // Clear the local entry
        entry.clear();
        showSnackBar("Dock ${entry.spot.spotName} cleared successfully!");

        // Refresh data
        getTrailerAudit();
        if (selectedPickUpLocation != null) {
          getSpots(locationId: selectedPickUpLocation!.locationId!, drop: false);
        }
      } else {
        showSnackBar("Failed to clear dock ${entry.spot.spotName}!", success: false);
        printLog('Clear dock failed: ${result.response}');
      }

      hideLoading();
    }
  }

  // Method to update dock entry
  void updateDockEntry(int index, {
    TruckDetail? truckDetail,
    FleetStatus? fleetStatus,
    String? notes,
  }) {
    if (index >= 0 && index < dockEntries.length) {
      if (truckDetail != null) {
        dockEntries[index].selectedTruckDetail = truckDetail;
      }
      if (fleetStatus != null) {
        dockEntries[index].selectedFleetStatus = fleetStatus;
      }
      if (notes != null) {
        dockEntries[index].notes = notes;
      }
      notifyListeners();
    }
  }

  postTrailerAudit() async {
    showLoading();
    var body = PostTrailerAudit(
      area: area,
      slot: slot,
      spotId: spotid,
      trailerStatus: trailerStatus,
      notes: notes,
      fleetId: selectedTruckDetail?.fleetId,
      carrier: carrier,
      locationId: selectedPickUpLocation?.locationId,
    );
    printLog(body.toJson());
    var result = await Services.postTrailerAudit(postTrailerAudit: body);
    if (result is Success) {
      showSnackBar("New trailer audit saved successfully!");
      clearAll();
      getTrailerAudit();
      getSpotOnLocations();
      refresh();
    }
    if (result is Failure) {
      printLog('Post trailer audit failed - ${result.response}');
      showSnackBar(
        "Failed to save new trailer audit!",
        success: false,
      );
    }
    clearAll();
    hideLoading();
    getTrailerAudit();
    getSpotOnLocations();
    refresh();
  }

  getTrailerAudit() async {
    showLoading();
    var result = await Services.getTrailerAudit();
    if (result is Success) {
      trailerAudit = result.response as GetTrailerAudit;
      printLog('Got ${trailerAudit?.list?.length ?? 0} trailer audit records');

      // If we have a selected location and dock entries, refresh them with new data
      if (selectedPickUpLocation != null && dockEntries.isNotEmpty) {
        createDockEntries();
      }

      refresh();
    }
    if (result is Failure) {
      printLog('Get trailer audit failed - ${result.response}');
      showSnackBar(
        "Failed to get trailer audit list!",
        success: false,
      );
    }
    hideLoading();
  }

  getSpots({required String locationId, bool drop = false}) async {
    showLoading();
    SpotOnClient? client = await Preferences.getSelectedClient();
    var result = await Services.getSpots(client!.clientId!, locationId);
    if (result is Success) {
      pickupSpotsList = result.response as SpotsList;
      printLog('Got ${pickupSpotsList.list.length} spots for location');

      // Get truck list and create dock entries after getting spots
      await getTruckList();

      // Create dock entries with prefilled data
      createDockEntries();
    }
    if (result is Failure) {
      printLog('Get Spots Failed');
    }
    hideLoading();
  }

  // Method to get truck list for dropdowns
  getTruckList() async {
    try {
      var result = await Services.getTruckListNew('');
      if (result is Success) {
        truckListModel = result.response as TruckListModel;
      }
      if (result is Failure) {
        printLog('Get Truck List Failed');
      }
    } catch (e) {
      printLog('Get Truck List Error: $e');
    }
  }

  getSpotOnLocations() async {
    showLoading();
    SpotOnClient? client = await Preferences.getSelectedClient();
    var result = await Services.getLocationSpots(client!.clientId!,'');
    if (result is Success) {
      spotOnLocationList = result.response as SpotOnLocationList;
    }
    if (result is Failure) {
      if (result.code == unauthorized) {
        openLogin();
        return;
      }
    }
    hideLoading();
  }

  // Method to save all dock entries using bulk API
  saveDockEntries() async {
    if (selectedPickUpLocation == null || dockEntries.isEmpty) {
      showSnackBar("No dock entries to save!", success: false);
      return;
    }

    // Find dock entries that have actual changes from original values
    List<DockEntry> changedEntries = [];
    List<String> validationErrors = [];

    for (DockEntry entry in dockEntries) {
      // Check if entry has actual changes compared to original values
      if (entry.hasChanges()) {
        changedEntries.add(entry);

        // Validate that all required fields are filled for changed entries
        if (!entry.hasAllRequiredFields()) {
          List<String> missingFields = [];

          if (entry.selectedTruckDetail == null) {
            missingFields.add("Trailer");
          }

          if (entry.selectedFleetStatus == null) {
            missingFields.add("Status");
          }

          String spotName = entry.spot.spotName ?? 'Unknown Dock';
          validationErrors.add("$spotName: Missing ${missingFields.join(', ')}");
        }
      }
    }

    // Check if there are any actual changes
    if (changedEntries.isEmpty) {
      showSnackBar("No changes found!", success: false);
      return;
    }

    // Check for validation errors
    if (validationErrors.isNotEmpty) {
      String errorMessage = "Please fill required fields:\n${validationErrors.join('\n')}";
      showSnackBar(errorMessage, success: false);
      return;
    }

    showLoading();

    // Prepare bulk trailer audit list with only changed entries
    List<BulkTrailerAuditItem> bulkList = [];

    for (DockEntry entry in changedEntries) {
      bulkList.add(BulkTrailerAuditItem(
        fleetId: entry.selectedTruckDetail?.fleetId,
        trailerStatus: entry.selectedFleetStatus?.id,
        notes: entry.notes,
        spotId: entry.spot.spotId,
      ));
    }

    printLog('Saving ${bulkList.length} changed dock entries via bulk API');
    printLog('Changed spots: ${changedEntries.map((e) => e.spot.spotName).join(', ')}');

    // Log the changes for debugging
    for (DockEntry entry in changedEntries) {
      printLog('${entry.spot.spotName}: Original[${entry.originalUnitNumber}, ${entry.originalFleetStatus}, "${entry.originalNotes}"] → Current[${entry.selectedTruckDetail?.unitNumber}, ${entry.selectedFleetStatus?.name}, "${entry.notes}"]');
    }

    // Call bulk save API
    var result = await Services.postBulkTrailerAudit(trailerAuditList: bulkList);

    if (result is Success) {
      // Update original values after successful save
      for (DockEntry entry in changedEntries) {
        entry.updateOriginalValues();
      }

      showSnackBar("${changedEntries.length} dock entries saved successfully!");
      // Refresh data
      getTrailerAudit();
      if (selectedPickUpLocation != null) {
        getSpots(locationId: selectedPickUpLocation!.locationId!, drop: false);
      }
    } else {
      showSnackBar("Failed to save dock entries!", success: false);
      printLog('Bulk save failed: ${result.response}');
    }

    hideLoading();
  }

  init() {}
}
